/* pages/index/index.wxss */

.greeting-card {
  /* 背景色通过内联样式动态设置 */
  text-align: center;
  position: relative;
  overflow: hidden;
}

.greeting-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10rpx); }
  60% { transform: translateY(-5rpx); }
}

.greeting {
  font-size: 36rpx;
  /* 颜色通过内联样式动态设置 */
  font-weight: 700;
  margin-bottom: 15rpx;
  /* text-shadow 通过内联样式动态设置 */
}

.greeting-decoration {
  display: flex;
  justify-content: center;
  gap: 20rpx;
}

.sparkle {
  font-size: 24rpx;
  animation: sparkle 1.5s ease-in-out infinite;
}

.sparkle:nth-child(2) {
  animation-delay: 0.5s;
}

.sparkle:nth-child(3) {
  animation-delay: 1s;
}

@keyframes sparkle {
  0%, 100% { opacity: 0.3; transform: scale(0.8); }
  50% { opacity: 1; transform: scale(1.2); }
}

.points-icon {
  font-size: 48rpx;
  margin-bottom: 10rpx;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.section-header {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: 700;
  /* 颜色通过内联样式动态设置 */
  /* text-shadow 通过内联样式动态设置 */
}

.section-more {
  font-size: 28rpx;
  /* 颜色通过内联样式动态设置 */
  font-weight: 600;
}

/* 习惯项样式 */
.habit-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.habit-item:last-child {
  border-bottom: none;
}

.habit-info {
  flex: 1;
}

.habit-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.habit-points {
  font-size: 24rpx;
  font-weight: bold;
}

.btn-complete {
  /* 背景色通过内联样式动态设置 */
  color: white;
  border-radius: 40rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  font-weight: 600;
  border: none;
  /* box-shadow 通过内联样式动态设置 */
  transition: all 0.3s ease;
}

.btn-complete:active {
  transform: translateY(2rpx);
  /* box-shadow 通过内联样式动态设置 */
}

/* 奖励项样式 */
.reward-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-info {
  flex: 1;
}

.reward-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.reward-points {
  font-size: 24rpx;
  font-weight: bold;
}

.btn-redeem {
  /* 背景色通过内联样式动态设置 */
  color: white;
  border-radius: 40rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border: none;
  /* box-shadow 通过内联样式动态设置 */
  transition: all 0.3s ease;
}

/* 记录项样式 */
.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-info {
  flex: 1;
}

.record-name {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.record-points {
  font-size: 24rpx;
  font-weight: bold;
}

.btn-cancel {
  background-color: #f44336;
  color: white;
  border-radius: 40rpx;
  padding: 12rpx 24rpx;
  font-size: 26rpx;
  border: none;
}

/* 空状态提示 */
.empty-hint {
  text-align: center;
  color: #999;
  font-size: 28rpx;
  padding: 40rpx 0;
}
