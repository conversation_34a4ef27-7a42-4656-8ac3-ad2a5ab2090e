/* pages/records/records.wxss */

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 统计信息 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.stat-item {
  text-align: center;
  padding: 30rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.stat-number {
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 8rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #666;
}

/* 筛选区域 */
.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.filter-label {
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
}

.filter-picker {
  display: flex;
  align-items: center;
  padding: 16rpx 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 26rpx;
  background-color: #fafafa;
  flex: 1;
}

.picker-arrow {
  color: #999;
  font-size: 20rpx;
  margin-left: auto;
}

.action-section {
  text-align: right;
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  border: none;
}

/* 记录项 */
.record-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.record-item:last-child {
  border-bottom: none;
}

.record-main {
  flex: 1;
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.record-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #333;
}

.record-points {
  font-size: 26rpx;
  font-weight: bold;
}

.record-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.record-type {
  font-size: 24rpx;
  color: #666;
  padding: 4rpx 12rpx;
  background-color: #f0f0f0;
  border-radius: 12rpx;
}

.record-time {
  font-size: 22rpx;
  color: #999;
}

.record-action {
  margin-left: 20rpx;
}

.btn-cancel-small {
  background-color: #f44336;
  color: white;
  border-radius: 6rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  border: none;
}
