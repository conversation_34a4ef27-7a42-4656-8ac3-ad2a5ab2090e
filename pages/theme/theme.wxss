/* pages/theme/theme.wxss */

.theme-page {
  min-height: 100vh;
  transition: background 0.3s ease;
}

/* 页面标题 */
.page-title {
  text-align: center;
  margin-bottom: 20rpx;
}

.title-icon {
  font-size: 48rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 36rpx;
  font-weight: 700;
  color: #333;
}

.page-subtitle {
  text-align: center;
  font-size: 26rpx;
  color: #666;
  margin-top: 10rpx;
}

/* 预览卡片 */
.preview-card {
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.preview-demo {
  padding: 20rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

/* 模拟积分显示 */
.demo-points {
  color: white;
  text-align: center;
  padding: 30rpx;
  border-radius: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.15);
}

.demo-points-number {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 8rpx;
}

.demo-points-label {
  font-size: 24rpx;
  opacity: 0.95;
}

/* 模拟按钮 */
.demo-buttons {
  display: flex;
  gap: 15rpx;
}

.demo-btn-primary,
.demo-btn-secondary {
  flex: 1;
  padding: 20rpx;
  border-radius: 25rpx;
  text-align: center;
  font-size: 26rpx;
  font-weight: 600;
  border: 2rpx solid transparent;
}

.demo-btn-primary {
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.2);
}

.demo-btn-secondary {
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
}

/* 主题网格 */
.section-title {
  font-size: 32rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 30rpx;
  text-align: center;
}

.themes-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

/* 主题项 */
.theme-item {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
  border: 3rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.theme-item.selected {
  border-color: #4CAF50;
  box-shadow: 0 6rpx 20rpx rgba(76, 175, 80, 0.3);
  transform: translateY(-2rpx);
}

.theme-item:active {
  transform: scale(0.98);
}

/* 主题图标 */
.theme-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 15rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.theme-icon-img {
  width: 48rpx;
  height: 48rpx;
}

/* 主题色彩预览 */
.theme-preview {
  display: flex;
  height: 60rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.theme-color-primary {
  flex: 2;
}

.theme-color-secondary {
  flex: 1;
}

.theme-color-bg {
  flex: 1;
}

/* 主题信息 */
.theme-info {
  text-align: center;
}

.theme-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.theme-emoji {
  font-size: 32rpx;
}

/* 选中标识 */
.theme-selected {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
}

.selected-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

/* 使用提示 */
.tips-card {
  margin-top: 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.tips-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  text-align: center;
}

.tips-list {
  space-y: 15rpx;
}

.tip-item {
  display: flex;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.tip-item:last-child {
  border-bottom: none;
}

.tip-icon {
  font-size: 28rpx;
  margin-right: 15rpx;
  width: 40rpx;
  text-align: center;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  flex: 1;
}
