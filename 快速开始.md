# 习惯小助手 - 快速开始指南

## 🚀 立即体验

### 1. 准备工作
1. 下载并安装 [微信开发者工具](https://developers.weixin.qq.com/miniprogram/dev/devtools/download.html)
2. 注册微信小程序开发账号（可选，本地调试不需要）

### 2. 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择本项目文件夹
4. 项目名称：习惯小助手
5. AppID：选择"测试号"（或填入你的小程序AppID）
6. 点击"导入"

### 3. 开始使用
项目导入后会自动编译，你可以立即在模拟器中体验：

#### 首次使用流程：
1. **查看首页** - 显示问候语和当前积分（初始为0）
2. **添加习惯** - 点击底部"习惯"标签，添加第一个习惯
3. **添加奖励** - 点击底部"奖励"标签，添加第一个奖励
4. **完成习惯** - 回到首页，完成习惯获得积分
5. **兑换奖励** - 积分足够时兑换奖励
6. **查看记录** - 在"记录"页面查看所有操作历史

## 📱 功能演示

### 使用模板快速开始
1. 进入"习惯"页面
2. 点击"使用模板"
3. 选择预设的习惯模板（如"早起"、"刷牙"等）
4. 同样在"奖励"页面使用奖励模板

### 自定义习惯和奖励
1. **添加习惯**：
   - 设置习惯名称（如"整理房间"）
   - 选择类型（好习惯获得积分，坏习惯扣除积分）
   - 设置积分数量（1-20分）

2. **添加奖励**：
   - 设置奖励名称（如"看动画片30分钟"）
   - 设置所需积分（10-200分）

### 积分系统体验
- 完成好习惯：获得积分 ✅
- 做了坏习惯：扣除积分 ❌
- 兑换奖励：消耗积分 🎁
- 取消记录：恢复积分 ↩️

## 🛠️ 开发调试

### 常用调试技巧
1. **查看存储数据**：
   - 在开发者工具中打开"调试器"
   - 选择"Storage" -> "Local Storage"
   - 查看所有存储的数据

2. **清空数据重新开始**：
   - 在小程序中进入"设置"页面
   - 点击"清空所有数据"
   - 或在调试器中清空 Local Storage

3. **模拟不同场景**：
   - 添加多个习惯和奖励
   - 完成习惯获得积分
   - 兑换奖励消耗积分
   - 测试取消操作

### 自定义修改
1. **修改主题色**：
   - 编辑 `app.wxss` 中的颜色变量
   - 主色调：`#4CAF50`（绿色）

2. **添加新功能**：
   - 在 `utils/util.js` 中添加工具函数
   - 在对应页面中实现新功能

3. **修改模板数据**：
   - 编辑 `app.js` 中的 `initTemplates()` 方法
   - 添加或修改习惯和奖励模板

## 📋 测试清单

### 基础功能测试
- [ ] 添加习惯（好习惯和坏习惯）
- [ ] 添加奖励
- [ ] 完成习惯获得积分
- [ ] 兑换奖励消耗积分
- [ ] 查看记录列表
- [ ] 取消记录恢复积分
- [ ] 使用模板添加习惯和奖励

### 边界情况测试
- [ ] 积分不足时无法兑换奖励
- [ ] 积分为负数时显示为0
- [ ] 删除习惯和奖励
- [ ] 清空所有数据
- [ ] 导出数据功能

### 界面测试
- [ ] 各页面正常显示
- [ ] 底部导航栏切换
- [ ] 表单输入验证
- [ ] 弹窗确认操作

## 🎯 下一步

1. **添加图标**：参考 `images/README.md` 添加导航栏图标
2. **个性化定制**：根据需要修改模板数据和界面样式
3. **发布上线**：申请小程序账号并发布到微信平台

## 💡 使用建议

### 适合年龄
- 主要面向 5-12 岁儿童
- 需要家长协助设置和监督

### 习惯建议
- 从简单的日常习惯开始（刷牙、洗脸）
- 逐步增加难度（阅读、运动）
- 设置合理的积分奖励

### 奖励建议
- 以精神奖励为主（看动画、游戏时间）
- 适当的物质奖励（小玩具、零食）
- 避免过度奖励影响内在动机

开始你的习惯养成之旅吧！🌟
