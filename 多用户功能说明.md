# 多用户功能说明

## 🎯 功能概述

习惯小助手现已支持多用户功能，可以在同一设备上管理多个用户的习惯数据，每个用户的数据完全独立，互不干扰。

## ✨ 主要特性

### 1. 用户管理
- ✅ **添加用户**：可以创建多个用户，每个用户有独立的名称和头像
- ✅ **删除用户**：可以删除不需要的用户（至少保留一个用户）
- ✅ **切换用户**：便捷地在不同用户之间切换
- ✅ **编辑用户**：修改用户名称和头像

### 2. 数据隔离
- ✅ **习惯隔离**：每个用户的习惯列表完全独立
- ✅ **奖励隔离**：每个用户的奖励列表完全独立
- ✅ **积分隔离**：每个用户的积分独立计算和存储
- ✅ **记录隔离**：每个用户的操作记录完全独立

### 3. 数据同步
- ✅ **习惯同步**：可以从其他用户复制习惯到当前用户
- ✅ **奖励同步**：可以从其他用户复制奖励到当前用户
- ✅ **选择性同步**：可以选择要同步的具体项目
- ✅ **批量同步**：支持一次性同步多个项目

### 4. 用户概览
- ✅ **积分概览**：快速查看所有用户的积分情况
- ✅ **数据统计**：显示每个用户的习惯、奖励、记录数量
- ✅ **当前用户标识**：清晰显示当前活跃用户

## 🚀 使用方法

### 添加新用户
1. 进入"设置"页面
2. 点击"用户管理"
3. 点击"➕ 添加用户"
4. 输入用户名并选择头像
5. 点击"确认"完成创建

### 切换用户
**方法一：快速切换**
1. 在"设置"页面找到当前用户信息
2. 点击"切换"按钮
3. 从弹出的列表中选择要切换的用户

**方法二：用户管理页面**
1. 进入"设置" → "用户管理"
2. 在用户列表中点击要切换的用户
3. 点击"切换"按钮

### 数据同步
1. 进入"设置" → "用户管理"
2. 点击"开始同步数据"
3. 选择要复制数据的源用户
4. 选择同步类型（习惯或奖励）
5. 勾选要同步的具体项目
6. 点击"🔄 开始同步"

### 管理用户
1. 进入"设置" → "用户管理"
2. 点击用户卡片可查看详情
3. 点击"编辑"可修改用户信息
4. 点击"删除"可删除用户（需确认）

## 📊 数据存储结构

### 用户信息存储
```javascript
users: [
  {
    id: "用户唯一标识",
    name: "用户名称",
    avatar: "头像emoji",
    createTime: "创建时间",
    totalPoints: "当前积分",
    themeKey: "主题设置"
  }
]
```

### 用户数据存储
```javascript
users_data.{userId}: {
  habits: [],      // 用户的习惯列表
  rewards: [],     // 用户的奖励列表
  records: [],     // 用户的记录列表
  totalPoints: 0   // 用户的积分
}
```

## 🔄 数据迁移

如果您之前使用的是单用户版本，系统会自动将您的数据迁移到多用户格式：

1. **自动检测**：系统启动时自动检测是否有旧格式数据
2. **创建默认用户**：自动创建名为"小公主"的默认用户
3. **数据迁移**：将所有旧数据迁移到默认用户下
4. **清理旧数据**：迁移完成后清理旧格式的存储数据

## 🛡️ 数据安全

- ✅ **完全隔离**：每个用户的数据完全独立，不会相互影响
- ✅ **本地存储**：所有数据存储在本地，保护隐私
- ✅ **数据备份**：支持导出功能，可备份所有用户数据
- ✅ **安全删除**：删除用户时会完全清理相关数据

## 💡 使用建议

### 适用场景
- 👨‍👩‍👧‍👦 **家庭使用**：父母和孩子分别管理自己的习惯
- 👫 **多人共享**：室友或朋友共用一个设备
- 🎭 **角色分离**：工作和生活习惯分开管理
- 📚 **教学场景**：老师为不同学生创建账户

### 最佳实践
1. **合理命名**：给用户起有意义的名称，便于识别
2. **选择头像**：为每个用户选择不同的头像，便于区分
3. **定期同步**：利用数据同步功能分享好的习惯模板
4. **及时切换**：使用前确认当前用户，避免数据混乱

## 🔧 技术实现

### 核心组件
- **userUtils**：用户管理工具函数
- **数据访问层重构**：所有数据操作支持用户隔离
- **用户管理页面**：完整的用户CRUD界面
- **数据同步页面**：用户间数据复制功能

### 兼容性
- ✅ **向后兼容**：自动迁移旧版本数据
- ✅ **平滑升级**：无需手动操作即可使用新功能
- ✅ **数据完整性**：确保数据迁移过程中不丢失任何信息

## 🎉 总结

多用户功能让习惯小助手变得更加灵活和实用，无论是个人使用还是家庭共享，都能提供完美的体验。每个用户都有独立的数据空间，同时又能方便地分享和同步有用的习惯模板。

开始使用多用户功能，让养成好习惯变得更有趣！
