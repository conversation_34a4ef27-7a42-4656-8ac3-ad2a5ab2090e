<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- 可爱的礼物盒图标 激活状态 -->
  <g>
    <!-- 礼物盒主体 -->
    <rect x="16" y="28" width="32" height="24" rx="4" fill="#4CAF50" stroke="#2E7D32" stroke-width="2"/>
    
    <!-- 礼物盒盖子 -->
    <rect x="14" y="24" width="36" height="8" rx="4" fill="#2E7D32"/>
    
    <!-- 蝴蝶结 -->
    <ellipse cx="24" cy="20" rx="6" ry="4" fill="#FF1493"/>
    <ellipse cx="40" cy="20" rx="6" ry="4" fill="#FF1493"/>
    <circle cx="32" cy="20" r="3" fill="#DC143C"/>
    
    <!-- 蝴蝶结中心装饰 -->
    <circle cx="32" cy="20" r="1.5" fill="#FFF0F5"/>
    
    <!-- 丝带 -->
    <rect x="30" y="24" width="4" height="28" fill="#FF1493"/>
    <rect x="14" y="26" width="36" height="4" fill="#FF1493"/>
    
    <!-- 可爱的装饰点 -->
    <circle cx="22" cy="36" r="2" fill="#E8F5E8"/>
    <circle cx="42" cy="36" r="2" fill="#E8F5E8"/>
    <circle cx="22" cy="44" r="2" fill="#E8F5E8"/>
    <circle cx="42" cy="44" r="2" fill="#E8F5E8"/>
    
    <!-- 小星星装饰 -->
    <g opacity="0.8">
      <path d="M20 32 L21 33 L22 33 L21.2 33.8 L21.5 35 L20 34.2 L18.5 35 L18.8 33.8 L18 33 L19 33 Z" fill="#FFD700"/>
      <path d="M44 40 L45 41 L46 41 L45.2 41.8 L45.5 43 L44 42.2 L42.5 43 L42.8 41.8 L42 41 L43 41 Z" fill="#FFD700"/>
    </g>
    
    <!-- 闪光效果 -->
    <g opacity="0.8">
      <circle cx="26" cy="30" r="1" fill="#FFFFFF"/>
      <circle cx="38" cy="38" r="1" fill="#FFFFFF"/>
      <circle cx="28" cy="46" r="1" fill="#FFFFFF"/>
      <circle cx="40" cy="32" r="1" fill="#FFFFFF"/>
    </g>
    
    <!-- 爱心装饰 -->
    <path d="M32 42 C31 41, 29 41, 29 43 C29 44, 32 46, 32 46 C32 46, 35 44, 35 43 C35 41, 33 41, 32 42 Z" fill="#FF69B4" opacity="0.8"/>
  </g>
</svg>
